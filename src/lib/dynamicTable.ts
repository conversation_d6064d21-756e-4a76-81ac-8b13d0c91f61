import { prisma } from './prisma'

export interface FieldMapping {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'text'
  required: boolean
  description?: string
}

export interface TableCreationResult {
  success: boolean
  tableName: string
  error?: string
}

/**
 * 根据字段映射创建动态数据表
 */
export async function createDynamicTable(
  formId: string,
  fieldMapping: Record<string, FieldMapping>
): Promise<TableCreationResult> {
  const tableName = `form_data_${formId}`
  
  try {
    // 构建SQL字段定义
    const fieldDefinitions: string[] = [
      'id BIGINT AUTO_INCREMENT PRIMARY KEY',
      'serial_number INT NOT NULL COMMENT "金数据序列号"',
      'created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间"',
      'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间"',
      'source_ip VARCHAR(45) NULL COMMENT "来源IP地址"',
      'creator_name VARCHAR(100) NULL COMMENT "创建者姓名"',
      'raw_data JSON NULL COMMENT "原始JSON数据"',
    ]

    // 为每个映射字段添加列定义
    Object.entries(fieldMapping).forEach(([key, config]) => {
      const sqlType = getSQLTypeFromFieldType(config.type)
      const nullable = config.required ? 'NOT NULL' : 'NULL'
      const comment = config.description ? `COMMENT "${config.description.replace(/"/g, '\\"')}"` : `COMMENT "${config.name}"`
      
      fieldDefinitions.push(`\`${key}\` ${sqlType} ${nullable} ${comment}`)
    })

    // 构建完整的CREATE TABLE语句
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS \`${tableName}\` (
        ${fieldDefinitions.join(',\n        ')},
        INDEX idx_serial_number (serial_number),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      COMMENT='表单数据表: ${formId}'
    `

    // 执行SQL语句创建表
    await prisma.$executeRawUnsafe(createTableSQL)

    console.log(`动态表 ${tableName} 创建成功`)

    return {
      success: true,
      tableName,
    }
  } catch (error) {
    console.error(`创建动态表 ${tableName} 失败:`, error)
    return {
      success: false,
      tableName,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 检查动态表是否存在
 */
export async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    const result = await prisma.$queryRawUnsafe<Array<{ table_name: string }>>(
      `SELECT table_name FROM information_schema.tables 
       WHERE table_schema = DATABASE() AND table_name = ?`,
      tableName
    )
    return result.length > 0
  } catch (error) {
    console.error(`检查表 ${tableName} 是否存在失败:`, error)
    return false
  }
}

/**
 * 删除动态表
 */
export async function dropDynamicTable(tableName: string): Promise<boolean> {
  try {
    await prisma.$executeRawUnsafe(`DROP TABLE IF EXISTS \`${tableName}\``)
    console.log(`动态表 ${tableName} 删除成功`)
    return true
  } catch (error) {
    console.error(`删除动态表 ${tableName} 失败:`, error)
    return false
  }
}

/**
 * 向动态表插入数据
 */
export async function insertDataToDynamicTable(
  tableName: string,
  data: Record<string, any>,
  fieldMapping: Record<string, FieldMapping>
): Promise<{ success: boolean; id?: number; error?: string }> {
  try {
    // 准备插入的数据
    const insertData: Record<string, any> = {
      serial_number: data.serial_number,
      source_ip: data.source_ip || null,
      creator_name: data.creator_name || null,
      raw_data: JSON.stringify(data),
    }

    // 根据字段映射转换数据
    Object.entries(fieldMapping).forEach(([key, config]) => {
      const value = data[key]
      insertData[key] = convertValueForDatabase(value, config.type)
    })

    // 构建字段名和占位符
    const fieldNames = Object.keys(insertData).map(name => `\`${name}\``).join(', ')
    const placeholders = Object.keys(insertData).map(() => '?').join(', ')
    const values = Object.values(insertData)

    // 执行插入操作
    const insertSQL = `INSERT INTO \`${tableName}\` (${fieldNames}) VALUES (${placeholders})`
    
    const result = await prisma.$executeRawUnsafe(insertSQL, ...values)
    
    // 获取插入的ID
    const [{ insertId }] = await prisma.$queryRawUnsafe<Array<{ insertId: bigint }>>(
      'SELECT LAST_INSERT_ID() as insertId'
    )

    return {
      success: true,
      id: Number(insertId),
    }
  } catch (error) {
    console.error(`向动态表 ${tableName} 插入数据失败:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 根据字段类型获取对应的SQL数据类型
 */
function getSQLTypeFromFieldType(fieldType: FieldMapping['type']): string {
  switch (fieldType) {
    case 'string':
      return 'VARCHAR(500)'
    case 'number':
      return 'DECIMAL(10,2)'
    case 'boolean':
      return 'BOOLEAN'
    case 'date':
      return 'DATETIME'
    case 'array':
      return 'JSON'
    case 'object':
      return 'JSON'
    case 'text':
      return 'TEXT'
    default:
      return 'VARCHAR(500)'
  }
}

/**
 * 根据字段类型转换值为数据库可存储的格式
 */
function convertValueForDatabase(value: any, fieldType: FieldMapping['type']): any {
  if (value === null || value === undefined) {
    return null
  }

  switch (fieldType) {
    case 'string':
      return String(value)
    case 'number':
      const num = Number(value)
      return isNaN(num) ? null : num
    case 'boolean':
      return Boolean(value)
    case 'date':
      if (typeof value === 'string') {
        const date = new Date(value)
        return isNaN(date.getTime()) ? null : date
      }
      return value instanceof Date ? value : null
    case 'array':
    case 'object':
      return typeof value === 'object' ? JSON.stringify(value) : String(value)
    case 'text':
      return String(value)
    default:
      return String(value)
  }
}